<?php

namespace app\api\controller\wanlshop;

use addons\wanlshop\library\WanlSdk\Common;
use addons\wanlshop\library\WanlSdk\Ehund;
use app\common\controller\Api;
use app\common\model\UserBill;
use think\Cache;
use think\Db;
use think\Exception;

/**
 * WanlShop订单接口
 */
class Order extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ["*"];

    /**
     * 获取订单列表
     *
     * @ApiSummary  (WanlShop 订单接口获取订单列表)
     * @ApiMethod   (GET)
     * 2020年5月12日23:25:40
     *
     * @param string $state 状态
     * @param string $delivery_type 配送方式：delivery=物流配送,pickup=线下自提,all=全部
     */
    public function getOrderList()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        $type = $this->request->param("type", "order");
        $status = $this->request->param("status", "");
        $delivery_type = $this->request->param("delivery_type", "");

        // 验证配送方式参数
        if (!empty($delivery_type) && !in_array($delivery_type, ['delivery', 'pickup', 'all'])) {
            $this->error(__("配送方式参数无效"));
        }

        if ($type == "commission") {
            if (!empty($status)) {
                $bill_order_ids = UserBill::where("user_id", $this->auth->id)
                    ->where("status", $status)
                    ->column("order_id");
                $where["id"] = ["in", $bill_order_ids];
            } else {
                $bill_order_ids = UserBill::where(
                    "user_id",
                    $this->auth->id
                )->column("order_id");
                $where["id"] = ["in", $bill_order_ids];
            }
        } else {
            $state = $this->request->request("state", "");
            if (!empty($state)) {
                $where["state"] = $state;
            }
            $where["user_id"] = $this->auth->id;
        }

        // 添加配送方式筛选条件
        if (!empty($delivery_type) && $delivery_type !== 'all') {
            $where["delivery_type"] = $delivery_type;
        }
        $user_id = $this->auth->id;

        // 列表
        $list = model("app\api\model\wanlshop\Order")
            ->where($where)
            // ->where(function($query) {
            //     $query->where('order_type', '<>', 1)
            //         ->whereOr('state', '<>', 1);
            // })
            ->field(
                "id,shop_id,state,order_type,taketime,is_no_shipping,delivery_type"
            )
            ->order("updatetime desc")
            ->paginate()
            ->each(function ($order, $key) use ($user_id) {
                $goods = model("app\api\model\wanlshop\OrderGoods")
                    ->where(["order_id" => $order->id])
                    ->field(
                        "id,title,goods_id,image,difference,price,market_price,number,refund_status"
                    )
                    ->select();
                $order["goods"] = $goods;
                // 获取支付 1.1.2升级
                $order["pay"] = model("app\api\model\wanlshop\Pay")
                    ->where(["order_id" => $order->id, "type" => "goods"])
                    ->field(
                        "pay_no,trade_no,pay_type,number, price, order_price, freight_price, discount_price, actual_payment"
                    )
                    ->find();
                $config = get_addon_config("wanlshop");

                // 避免间接修改错误，先检查pay是否存在
                if ($order["pay"]) {
                    $payArray = $order["pay"]->toArray();
                    $payArray["mch_id"] = $config["sdk_qq"]["mch_id"];
                    $order["pay"] = $payArray;
                }
                $order["shop"] = $order->shop
                    ? $order->shop->visible(["shopname"])
                    : [];
                $commission = UserBill::where("order_id", $order->id)
                    ->where("user_id", $user_id)
                    ->find();
                if ($commission) {
                    $commission->time = date(
                        "Y-m-d H:i:s",
                        $commission->updatetime
                    );
                }
                $order["commission"] = $commission;
                if ($order["taketime"]) {
                    $taketime = date("Y-m-d H:i:s", $order["taketime"]);
                } else {
                    $taketime = 0;
                }
                $order["taketime"] = $taketime;

                // 获取取货码（只有已支付的自提订单才返回）
                $order["pickup_code"] = null;
                if ($order["state"] >= 2 && $order["delivery_type"] === "pickup") {
                    $pickupCode = model("app\api\model\wanlshop\PickupCode")
                        ->where([
                            "order_id" => $order->id,
                            "order_type" => "goods",
                            "status" => "active"
                        ])
                        ->field("pickup_code, qr_image, status, expire_time")
                        ->find();

                    if ($pickupCode) {
                        // 使用临时变量避免间接修改错误
                        $pickupCodeArray = $pickupCode->toArray();
                        // 添加二维码完整URL
                        if ($pickupCode["qr_image"]) {
                            $pickupCodeArray["qr_image_url"] = request()->domain() . $pickupCode["qr_image"];
                        }
                        $order["pickup_code"] = $pickupCodeArray;
                    }
                }

                return $order;
            });
        $list ? $this->success("ok", $list) : $this->error(__("网络繁忙"));
    }

    /**
     * 获取购买过的商品
     *
     * @ApiSummary  (WanlShop 订单接口获取订单列表)
     * @ApiMethod   (GET)
     * 2020年5月12日23:25:40
     *
     * @param string $state 状态
     */
    public function getBuyList()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        $order_ids = [];
        foreach (
            model("app\api\model\wanlshop\Order")
                ->where([
                    "user_id" => ["eq", $this->auth->id],
                    "state" => ["in", "2,3,4,6"],
                    "status" => ["eq", "normal"],
                ])
                ->select()
            as $order
        ) {
            $order_ids[] = $order["id"];
        }
        $goods = model("app\api\model\wanlshop\OrderGoods")
            ->where("order_id", "in", $order_ids)
            ->select();
        // 列表
        $list = model("app\api\model\wanlshop\Goods")
            ->where(
                "id",
                "in",
                array_keys(array_flip(array_column($goods, "goods_id")))
            )
            ->field("id, image, title, price")
            ->order("updatetime desc")
            ->paginate();
        $this->success("ok", $list);
    }

    /**
     * 查询用户店铺订单记录
     *
     * @ApiSummary  (查询用户店铺订单记录 1.0.2升级)
     * @ApiMethod   (POST)
     *
     * @param string $shop_id 店铺ID
     */
    public function getOrderListToShop()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $shop_id = $this->request->post("shop_id");
            $shop_id ? "" : $this->error(__("Invalid parameters"));
            $list = model("app\api\model\wanlshop\Order")
                ->where([
                    "shop_id" => $shop_id,
                    "user_id" => $this->auth->id,
                    "status" => "normal",
                ])
                ->field("id,shop_id,order_no,state")
                ->order("updatetime desc")
                ->select();
            // 订单状态:1=待支付,2=待发货,3=待收货,4=待评论,5=售后订单(已弃用),6=已完成,7=已取消
            foreach ($list as $row) {
                $row["goods"] = model("app\api\model\wanlshop\OrderGoods")
                    ->where(["order_id" => $row->id])
                    ->field(
                        "id,title,image,difference,price,market_price,number,refund_status"
                    )
                    ->select();
            }
            $this->success(__("发送成功"), $list);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 获取订单详情
     *
     * @ApiSummary  (WanlShop 订单接口获取订单详情)
     * @ApiMethod   (GET)
     *
     * @param string $id 订单ID
     */
    public function getOrderInfo()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        $id = $this->request->request("id");
        $order_no = $this->request->request("order_no");
        //        $id ? $id : ($this->error(__('非法请求')));
        if ($order_no) {
            $id = model("app\api\model\wanlshop\Pay")
                ->where("pay_no", $order_no)
                ->find()["order_id"];
        }

        $order = model("app\api\model\wanlshop\Order")
            ->where("id", $id)
            ->where("user_id", $this->auth->id)
            ->field(
                "id,shop_id,order_no,isaddress,express_no,express_name,freight_type,state,order_type,createtime,paymenttime,delivertime,taketime,dealtime,is_no_shipping,delivery_type"
            )
            ->find();
        $order ? $order : $this->error(__("网络异常"));
        // 输出配置
        $config = get_addon_config("wanlshop");
        $order["config"] = $config["order"];
        switch ($order["state"]) {
            case 1:
                $express = [
                    "context" => "付款后，即可将宝贝发出",
                    "status" => "尚未付款",
                    "time" => date("Y-m-d H:i:s", $order["createtime"]),
                ];
                break;
            case 2:
                $express = [
                    "context" => "商家正在处理订单",
                    "status" => "已付款",
                    "time" => date("Y-m-d H:i:s", $order["paymenttime"]),
                ];
                break;
            default:
                // 获取物流
                $eData = model("app\api\model\wanlshop\KuaidiSub")
                    ->where(["express_no" => $order["express_no"]])
                    ->find();
                // 兼容PHP7.4	1.1.5升级
                $kdInfo = Ehund::queryKd(
                    $order["express_no"],
                    $order["express_name"]
                );
                $ybData = json_decode($kdInfo, true)["result"];
                // 获取数据 兼容PHP7.4	1.1.5升级
                if (!empty($ybData["list"])) {
                    // 运单状态 1.0.6升级
                    $statusText = [
                        "在途",
                        "揽收",
                        "疑难",
                        "签收",
                        "退签",
                        "派件",
                        "退回",
                        "转投",
                    ];
                    if ($ybData["status"] == 3) {
                        $status = "在途";
                    } else {
                        $status = "已签收";
                    }
                    $express = [
                        "status" => $status,
                        "context" => $ybData[0]["status"],
                        "time" => $ybData[0]["time"],
                    ];
                } else {
                    $express = [
                        "status" => "已发货",
                        "context" => "包裹正在等待快递小哥揽收~",
                        "time" => date("Y-m-d H:i:s", $order["delivertime"]),
                    ];
                }
        }
        // 获取物流
        $order["logistics"] = $express;
        // 获取地址
        $order["address"] = model("app\api\model\wanlshop\OrderAddress")
            ->where(["order_id" => $id, "user_id" => $this->auth->id])
            ->order("isaddress desc")
            ->field("id,name,mobile,address,address_name")
            ->find();
        // 获取店铺
        $order["shop"] = $order->shop;
        // 获取订单商品
        $order["goods"] = model("app\api\model\wanlshop\OrderGoods")
            ->where(["order_id" => $id])
            ->field(
                "id,goods_id,title,image,difference,price,market_price,actual_payment,discount_price,freight_price,number,refund_id,refund_status"
            )
            ->select();
        // 获取支付 1.1.2升级
        $order["pay"] = model("app\api\model\wanlshop\Pay")
            ->where(["order_id" => $order->id, "type" => "goods"])
            ->field(
                "id, pay_no, number, price, order_price, freight_price, discount_price, actual_payment"
            )
            ->find();

        // 获取取货码（只有已支付的自提订单才返回）
        $order["pickup_code"] = null;
        if ($order["state"] >= 2 && $order["delivery_type"] === "pickup") {
            $pickupCode = model("app\api\model\wanlshop\PickupCode")
                ->where([
                    "order_id" => $order->id,
                    "order_type" => "goods",
                    "status" => "active"
                ])
                ->field("pickup_code, qr_image, status, expire_time")
                ->find();

            if ($pickupCode) {
                // 使用临时变量避免间接修改错误
                $pickupCodeArray = $pickupCode->toArray();
                // 添加二维码完整URL
                if ($pickupCode["qr_image"]) {
                    $pickupCodeArray["qr_image_url"] = request()->domain() . $pickupCode["qr_image"];
                }
                $order["pickup_code"] = $pickupCodeArray;
            }
        }

        $this->success("ok", $order);
    }

    /**
     * 确认收货
     *
     * @ApiSummary  (WanlShop 订单接口确认收货)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function confirmOrder()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $id = $this->request->post("id");
            $id ? $id : $this->error(__("非法请求"));
            // 判断权限
            $order = model("app\api\model\wanlshop\Order")
                ->where([
                    "id" => $id,
                    "state" => 3,
                    "user_id" => $this->auth->id,
                ])
                ->find();
            if (!$order) {
                $this->error(__("订单异常"));
            }
            Db::startTrans();
            try {
                // 获取支付 1.1.2升级
                $pay = model("app\api\model\wanlshop\Pay")->get([
                    "order_id" => $id,
                    "type" => "goods",
                ]);
                // 平台转款给商家
                controller("addons\wanlshop\library\WanlPay\WanlPay")->money(
                    +$pay["price"],
                    $order["shop"]["user_id"],
                    "买家确认收货",
                    "pay",
                    $order["order_no"]
                );
                // 查询是否有退款
                $refund = model("app\api\model\wanlshop\Refund")
                    // ->where(['order_id' => $id, 'state' => 4, 'order_type' => 'goods'])
                    ->where(["order_id" => $id, "order_type" => "goods"])
                    ->whereIn("state", [0, 1, 2, 3, 6, 7])
                    ->select();
                // 退款存在
                if ($refund) {
                    foreach ($refund as $value) {
                        // 关闭退款
                        $value->state = 5;
                        $value->save();

                        // 关闭退款商品
                        $goodsIds = explode(",", $value->goods_ids);
                        model("app\api\model\wanlshop\OrderGoods")
                            ->where("id", "in", $goodsIds)
                            ->where("order_id", $id)
                            ->update(["refund_status" => 4]);

                        \think\Log::info(
                            "关闭退款商品, sql:" .
                                model("app\api\model\wanlshop\OrderGoods")
                                    ->where("goods_id", "in", $goodsIds)
                                    ->where("order_id", $id)
                                    ->buildSql()
                        );

                        // 执行退款 - 更新为关闭退款
                        // controller('addons\wanlshop\library\WanlPay\WanlPay')->money(-$value['price'], $order['shop']['user_id'], '该订单存在的退款', 'pay', $order['order_no']);
                    }
                }
                // 更新退款
                $order->save(
                    ["state" => 4, "taketime" => time()],
                    ["id" => $id]
                );
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success("ok", $order ? true : false);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 取消订单
     *
     * @ApiSummary  (WanlShop 订单接口取消订单)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function cancelOrder()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $id = $this->request->post("id");
            $id ? $id : $this->error(__("非法请求"));
            // 判断权限
            $this->getOrderState($id) != 1 ? $this->error(__("订单异常")) : "";
            $row = model("app\api\model\wanlshop\Order")->get([
                "id" => $id,
                "user_id" => $this->auth->id,
            ]);

            // 初始化扣除结果
            $deductResult = ['deducted' => false, 'message' => '订单取消成功！'];

            \think\Db::startTrans();
            try {
                $result = $row->allowField(true)->save(["state" => 7]);

                // 还原优惠券 1.0.2升级
                if ($row["coupon_id"] != 0) {
                    model("app\api\model\wanlshop\CouponReceive")
                        ->where([
                            "id" => $row["coupon_id"],
                            "user_id" => $this->auth->id,
                        ])
                        ->update(["state" => 1]);
                }

                // 退还抵扣余额
                $this->refundDeductionBalanceForOrder($id, $this->auth->id, '取消订单退还抵扣余额');

                // 扣除赠送的抵扣券
                $deductResult = $this->deductRewardedBalanceForOrder($id, $this->auth->id);
                if (!$deductResult['success']) {
                    throw new \Exception($deductResult['message']);
                }

                \think\Db::commit();
            } catch (\Exception $e) {
                \think\Db::rollback();
                $this->error("取消订单失败：" . $e->getMessage());
            }
            // 释放库存 1.0.3升级
            foreach (
                model("app\api\model\wanlshop\OrderGoods")->all([
                    "order_id" => $row["id"],
                ])
                as $vo
            ) {
                // 查询商品是否需要释放库存
                if (
                    model("app\api\model\wanlshop\Goods")->get($vo["goods_id"])[
                        "stock"
                    ] == "porder"
                ) {
                    model("app\api\model\wanlshop\GoodsSku")
                        ->where("id", $vo["goods_sku_id"])
                        ->setInc("stock", $vo["number"]);
                }
            }

            // 根据是否扣除了抵扣券返回不同的成功消息
            $successMessage = $deductResult['deducted'] ? $deductResult['message'] : "订单取消成功！";
            $this->success($successMessage, $result ? true : false);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 删除订单
     *
     * @ApiSummary  (WanlShop 订单接口删除订单)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function delOrder()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $id = $this->request->post("id");
            $id ? $id : $this->error(__("非法请求"));
            // 判断权限
            $state = $this->getOrderState($id);
            $state == 6 || $state == 7 ? "" : $this->error(__("非法请求"));
            $order = model("app\api\model\wanlshop\Order")->save(
                ["status" => "hidden"],
                ["id" => $id]
            );
            $this->success("ok", $order ? true : false);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 修改地址
     *
     * @ApiSummary  (WanlShop 订单接口修改地址)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function editOrderAddress()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $order_id = $this->request->post("id");
            $address_id = $this->request->post("address_id");
            $order_id || $address_id ? $order_id : $this->error(__("非法请求"));
            // 判断权限
            $this->getOrderState($order_id) > 3
                ? $this->error(__("订单异常"))
                : "";
            // 订单
            $order = model("app\api\model\wanlshop\Order")
                ->where([
                    "id" => $order_id,
                    "user_id" => $this->auth->id,
                ])
                ->find();

            //判断是否修改过
            if ($order["isaddress"] == 1) {
                $this->error(__("已经修改过一次了"));
            } else {
                // 获取地址
                $address = model("app\api\model\wanlshop\Address")
                    ->where(["id" => $address_id, "user_id" => $this->auth->id])
                    ->find();
                // 修改地址
                $data = model("app\api\model\wanlshop\OrderAddress")->save([
                    "user_id" => $this->auth->id,
                    "shop_id" => $order->shop_id,
                    "order_id" => $order_id,
                    "isaddress" => 1,
                    "name" => $address["name"],
                    "mobile" => $address["mobile"],
                    "address" =>
                        $address["province"] .
                        "/" .
                        $address["city"] .
                        "/" .
                        $address["district"] .
                        "/" .
                        $address["address"],
                    "address_name" => $address["address_name"],
                    "location" => $address["location"],
                ]);
                // 修改状态
                model("app\api\model\wanlshop\Order")
                    ->where([
                        "id" => $order_id,
                        "user_id" => $this->auth->id,
                    ])
                    ->update(["isaddress" => 1]);
                $this->success("ok", $data);
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 评论订单
     *
     * @ApiSummary  (WanlShop 订单接口评论订单)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function commentOrder()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post ? $post : $this->error(__("数据异常"));
            $user_id = $this->auth->id;
            // 判断权限
            $this->getOrderState($post["order_id"]) != 4
                ? $this->error(__("已经评论过或订单异常"))
                : "";
            // 生成列表
            $commentData = [];
            foreach ($post["goodsList"] as $value) {
                $commentData[] = [
                    "user_id" => $user_id,
                    "shop_id" => $post["shop"]["id"],
                    "order_id" => $post["order_id"],
                    "goods_id" => $value["goods_id"],
                    "order_goods_id" => $value["id"],
                    "order_type" => "goods",
                    "state" => $value["state"],
                    "content" => $value["comment"],
                    "suk" => $value["difference"],
                    "images" => $value["imgList"],
                    "score" => round(
                        ($post["shop"]["describe"] +
                            $post["shop"]["service"] +
                            $post["shop"]["deliver"] +
                            $post["shop"]["logistics"]) /
                            4,
                        1
                    ),
                    "score_describe" => $post["shop"]["describe"],
                    "score_service" => $post["shop"]["service"],
                    "score_deliver" => $post["shop"]["deliver"],
                    "score_logistics" => $post["shop"]["logistics"],
                    "switch" => 0,
                ];
                //评论暂不考虑并发，为列表提供好评付款率，减少并发只能写进商品中
                model("app\api\model\wanlshop\Goods")
                    ->where(["id" => $value["goods_id"]])
                    ->setInc("comment");
                if ($value["state"] == 0) {
                    model("app\api\model\wanlshop\Goods")
                        ->where(["id" => $value["goods_id"]])
                        ->setInc("praise");
                } elseif ($value["state"] == 1) {
                    model("app\api\model\wanlshop\Goods")
                        ->where(["id" => $value["goods_id"]])
                        ->setInc("moderate");
                } elseif ($value["state"] == 2) {
                    model("app\api\model\wanlshop\Goods")
                        ->where(["id" => $value["goods_id"]])
                        ->setInc("negative");
                }
            }
            if (
                model("app\api\model\wanlshop\GoodsComment")->saveAll(
                    $commentData
                )
            ) {
                $order = model("app\api\model\wanlshop\Order")
                    ->where([
                        "id" => $post["order_id"],
                        "user_id" => $user_id,
                    ])
                    ->update(["state" => 6]);
            }
            //更新店铺评分
            $score = model("app\api\model\wanlshop\GoodsComment")
                ->where(["user_id" => $user_id])
                ->select();
            // 从数据集中取出
            $describe = array_column($score, "score_describe");
            $service = array_column($score, "score_service");
            $deliver = array_column($score, "score_deliver");
            $logistics = array_column($score, "score_logistics");
            // 更新店铺评分
            model("app\api\model\wanlshop\Shop")
                ->where(["id" => $post["shop"]["id"]])
                ->update([
                    "score_describe" => bcdiv(
                        array_sum($describe),
                        count($describe),
                        1
                    ),
                    "score_service" => bcdiv(
                        array_sum($service),
                        count($service),
                        1
                    ),
                    "score_deliver" => bcdiv(
                        array_sum($deliver),
                        count($deliver),
                        1
                    ),
                    "score_logistics" => bcdiv(
                        array_sum($logistics),
                        count($logistics),
                        1
                    ),
                ]);
            $this->success("ok", []);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 获取订单物流状态
     *
     * @ApiSummary  (WanlShop 订单接口获取订单物流状态)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function getLogistics()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $id = $this->request->post("id");
            $id ? $id : $this->error(__("非法请求"));
            //获取订单
            $order = model("app\api\model\wanlshop\Order")
                ->where([
                    "id" => $id,
                    "user_id" => $this->auth->id,
                ])
                ->field(
                    "id,shop_id,express_name,express_no,order_no,createtime,paymenttime,delivertime"
                )
                ->find();
            // 获取快递
            switch ($order["state"]) {
                case 1:
                    $express[] = [
                        "context" => "付款后，即可将宝贝发出",
                        "status" => "尚未付款",
                        "time" => date("Y-m-d H:i:s", $order["createtime"]),
                    ];
                    break;
                case 2:
                    $express[] = [
                        "context" => "商家接受到您的订单，准备出库",
                        "status" => "已下单",
                        "time" => date("Y-m-d H:i:s", $order["paymenttime"]),
                    ];
                    break;
                default:
                    // 获取物流
                    $express = model("app\api\model\wanlshop\KuaidiSub")
                        ->where(["express_no" => $order["express_no"]])
                        ->find();
                    $data = Ehund::queryKd(
                        $order["express_no"],
                        $order["express_name"]
                    );
                    $kd_list = json_decode($data, true)["result"];
                    if ($kd_list["list"]) {
                        foreach ($kd_list["list"] as $v) {
                            $express[] = [
                                "context" => $v["status"],
                                "status" =>
                                    $kd_list["deliverystatus"] == 1
                                        ? "在途中"
                                        : "已签收",
                                "time" => date(
                                    "Y-m-d H:i:s",
                                    $order["delivertime"]
                                ),
                            ];
                        }
                    } else {
                        $express[] = [
                            "context" => "打包完成，正在等待快递小哥揽收~",
                            "status" => "仓库处理中",
                            "time" => date(
                                "Y-m-d H:i:s",
                                $order["delivertime"]
                            ),
                        ];
                    }
            }
            $order["kuaidi"] = $order->kuaidi
                ? $order->kuaidi->visible(["name", "logo", "tel"])
                : [];
            $order["express"] = $express;
            $this->success("ok", $order);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 确认订单
     *
     * @ApiSummary  (WanlShop 订单接口确认订单)
     * @ApiMethod   (POST)
     *
     * @param string $data 商品数据
     */
    public function getOrderGoodsList()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $request = $this->request->post();
            // 订单数据
            $order = [];
            $map = [];
            // 1.0.5升级 修复客户端地址更新
            $where = !empty($request["address_id"])
                ? ["id" => $request["address_id"], "user_id" => $this->auth->id]
                : ["user_id" => $this->auth->id, "default" => 1];
            // 地址
            $address = model("app\api\model\wanlshop\Address")
                ->where($where)
                ->field("id,name,mobile,province,city,district,address")
                ->find();
            // 1.1.6升级
            if (!$address) {
                $this->error(__("请添加收货地址"));
            }
            // 合计
            $statis = [
                "allnum" => 0,
                "allsub" => 0,
            ];
            foreach ($request["data"] as $post) {
                $redis = Common::redis();
                // 商品详情
                $goods = model("app\api\model\wanlshop\Goods")
                    ->where("id", $post["goods_id"])
                    ->field(
                        "id, shop_id, shop_category_id, activity_type, title,image,stock,freight_id,sales"
                    )
                    ->find();
                // 获取SKU
                $sku = model("app\api\model\wanlshop\GoodsSku")
                    ->where("id", $post["sku_id"])
                    ->field(
                        "id,goods_id,difference,price,market_price,stock,weigh"
                    )
                    ->find();

                // 1.1.2升级 判断是否超出库存
                $sku_key = "goods_" . $sku["goods_id"] . "_" . $sku["id"];
                if ($post["number"] > $redis->llen("{$sku_key}")) {
                    $this->error("系统繁忙，请稍后抢购！");
                }
                // 处理运费模板：如果freight_id为0，为该店铺创建默认免费模板
                $freightId = $goods["freight_id"];
                if ($freightId == 0) {
                    $defaultFreightId = $this->createDefaultFreeFreightTemplate($goods['shop_id']);
                    if ($defaultFreightId) {
                        // 更新商品的运费模板ID
                        model("app\api\model\wanlshop\Goods")
                            ->where("id", $goods["id"])
                            ->update(['freight_id' => $defaultFreightId]);
                        $freightId = $defaultFreightId;
                    }
                }

                // 获取快递及价格
                $goods["freight"] = $this->freight(
                    $freightId,
                    $sku["weigh"],
                    $post["number"],
                    $address["city"]
                );
                // 获取SKU
                $goods["sku"] = $sku;
                // 数量
                $goods["number"] = $post["number"];
                // 格式化
                if (empty($map[$goods["shop_id"]])) {
                    $order[] = [
                        "shop_id" => $goods["shop_id"],
                        "shop" => $goods->shop
                            ? $goods->shop->visible([
                                "shopname",
                                "contact_phone",
                                "business_start_time",
                                "business_end_time",
                                "business_hours_text",
                                "is_open_now",
                                "address",
                                "avatar",
                                "lng",
                                "lat",
                                "city",
                            ])
                            : [],
                        "products" => [$goods],
                        "coupon" => [],
                        "freight" => [$goods["freight"]],
                        "number" => $goods["number"],
                        "sub_price" => bcmul(
                            $sku["price"],
                            $goods["number"],
                            2
                        ),
                    ];
                    $map[$goods["shop_id"]] = $goods;
                } else {
                    // 追加1-*
                    foreach ($order as $key => $value) {
                        if ($value["shop_id"] == $goods["shop_id"]) {
                            array_push($order[$key]["products"], $goods);
                            array_push(
                                $order[$key]["freight"],
                                $goods["freight"]
                            );
                            $order[$key]["number"] += $post["number"];
                            $order[$key]["sub_price"] = bcadd(
                                $order[$key]["sub_price"],
                                bcmul($sku["price"], $post["number"], 2),
                                2
                            );
                            break;
                        }
                    }
                }
                // 所有店铺统计
                $statis["allnum"] += $goods["number"];
            }
            // 获取运费策略-店铺循环
            foreach ($order as $key => $value) {
                $config = model("app\api\model\wanlshop\ShopConfig")
                    ->where("shop_id", $value["shop_id"])
                    ->find();

                // 如果店铺配置不存在，自动创建默认配置
                if (!$config) {
                    $config = $this->createDefaultShopConfig($value["shop_id"]);
                }

                if ($config) {
                    if ($config["freight"] == 0) {
                        // 运费叠加
                        $order[$key]["freight"] = [
                            "id" => $value["freight"][0]["id"],
                            "name" => "运费叠加",
                            "price" => array_sum(
                                array_column($value["freight"], "price")
                            ),
                        ];
                    } elseif ($config["freight"] == 1) {
                        // 以最低结算
                        array_multisort(
                            array_column($value["freight"], "price"),
                            SORT_ASC,
                            $value["freight"]
                        );
                        $order[$key]["freight"] = [
                            "id" => $value["freight"][0]["id"],
                            "name" => $value["freight"][0]["name"],
                            "price" => $value["freight"][0]["price"],
                        ];
                    } elseif ($config["freight"] == 2) {
                        // 以最高结算
                        array_multisort(
                            array_column($value["freight"], "price"),
                            SORT_DESC,
                            $value["freight"]
                        );
                        $order[$key]["freight"] = [
                            "id" => $value["freight"][0]["id"],
                            "name" => $value["freight"][0]["name"],
                            "price" => $value["freight"][0]["price"],
                        ];
                    }
                    $order[$key]["order_price"] = $order[$key]["sub_price"];
                    // 2020年9月19日12:10:59 添加快递价格备份,用于还原运费
                    $order[$key]["freight_price_bak"] =
                        $order[$key]["freight"]["price"];
                    // 1.0.8升级
                    $order[$key]["sub_price"] = bcadd(
                        $order[$key]["sub_price"],
                        $order[$key]["freight"]["price"],
                        2
                    );
                    $statis["allsub"] = bcadd(
                        $statis["allsub"],
                        $order[$key]["sub_price"],
                        2
                    );
                } else {
                    $this->error(__("商家未配置运费组策略，暂不支持下单"));
                }
            }
            // 传递Token
            $datalist["token"] = Common::creatToken(
                "orderToken_" . $this->auth->id
            );
            // 地址
            $datalist["addressData"] = $address;
            // 订单
            $datalist["orderData"]["lists"] = $order;
            $datalist["orderData"]["statis"] = $statis;
            // 配送方式选项
            $orderModel = new \app\api\model\wanlshop\Order();
            $datalist["deliveryTypes"] = $orderModel->getDeliveryTypeList();
            $this->success("ok", $datalist);
        } else {
            $this->error(__("非法请求"));
        }
    }

    /**
     * 提交订单
     *
     * @ApiSummary  (WanlShop 订单接口提交订单)
     * @ApiMethod   (POST)
     *
     * @param string $data 数组
     */
    public function addOrder()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $result = false;
            $params = $this->request->post();
            // 验证Token
            if (array_key_exists("token", $params)) {
                if (
                    !Common::checkToken(
                        $params["token"],
                        "orderToken_" . $this->auth->id
                    )
                ) {
                    $this->error(__("页面安全令牌已过期！请重返此页"));
                }
            } else {
                $this->error(__("非法提交，未传入Token"));
            }
            $user_id = $this->auth->id; // 用户ID
            $addressList = [];
            $goodsList = [];
            $payList = [];
            // 判断是否有地址 1.0.2升级
            if (array_key_exists("address_id", $params["order"])) {
                $address_id = $params["order"]["address_id"]; // 地址ID
            } else {
                $this->error(__("请点击上方添加收货地址"));
            }
            // 判断订单是否合法 1.0.4升级
            if (array_key_exists("lists", $params["order"])) {
                $lists = $params["order"]["lists"];
                if (!isset($lists) && count($lists) == 0) {
                    $this->error(
                        __("订单繁忙ERR001：请返回商品详情重新提交订单")
                    );
                }
            } else {
                $this->error(__("订单繁忙ERR002：请返回商品详情重新提交订单"));
            }
            // 查询地址
            $address = model("app\api\model\wanlshop\Address")
                ->where(["id" => $address_id, "user_id" => $user_id])
                ->find();
            // 1.0.4升级
            if (!isset($address)) {
                $this->error(__("地址异常，没有找到该地址"));
            }
            //          判断该地址是否可配送

            // 数据库事务操作
            Db::startTrans();
            try {
                // 遍历已店铺分类列表
                foreach ($lists as $item) {
                    // 获取店铺ID
                    $shop_id = $item["shop_id"];
                    // 查询店铺配置
                    $config = model("app\api\model\wanlshop\ShopConfig")
                        ->where("shop_id", $shop_id)
                        ->find();
                    // 如果不存在，按照累计运费
                    if (!$config) {
                        $config["freight"] = 0;
                    }
                    // 生成订单
                    $order = new \app\api\model\wanlshop\Order();
                    $order->freight_type = $config["freight"];
                    $order->user_id = $user_id;
                    $order->shop_id = $shop_id;
                    $order->order_no = $shop_id . $user_id;
                    if (isset($item["remarks"])) {
                        $order->remarks = $item["remarks"];
                    }
                    // 2021年3月04日 06:54:11 修改优惠券逻辑
                    $coupon = model("app\api\model\wanlshop\CouponReceive")
                        ->where([
                            "id" => $item["coupon_id"],
                            "user_id" => $user_id,
                            "shop_id" => $shop_id,
                        ])
                        ->find();
                    $order->coupon_id = $coupon ? $coupon["id"] : 0;

                    // 处理配送方式
                    $delivery_type = $item["delivery_type"] ?? "delivery";
                    $order->delivery_type = $delivery_type;

                    // 要补充活动ID
                    if ($order->save()) {
                        $priceAll = 0; // 总价格
                        $numberAll = 0; // 总数量
                        $freightALL = [];
                        $shopGoodsAll = [];
                        // 计算订单价格
                        foreach ($item["products"] as $data) {
                            $redis = Common::redis();
                            // 查询商品 1.2.0升级
                            $goods = model("app\api\model\wanlshop\Goods")
                                ->with(["brand"])
                                ->where([
                                    "goods.id" => $data["goods_id"],
                                    "goods.status" => "normal",
                                ])
                                ->find();
                            // 获取sku
                            $sku = model(
                                "app\api\model\wanlshop\GoodsSku"
                            )->get($data["sku_id"]);
                            $orderGoodss = model(
                                "app\api\model\wanlshop\OrderGoods"
                            )
                                ->where([
                                    "goods_sku_id" => $data["sku_id"],
                                    "refund_status" => ["in", [0, 1, 2, 5]],
                                ])
                                ->select();
                            // 购买数量
                            $orderGoodssNum = 0;
                            foreach ($orderGoodss as $key => $orderGoods) {
                                if (
                                    in_array($orderGoods["order"]["state"], [7])
                                ) {
                                    unset($orderGoodss[$key]);
                                    continue;
                                }

                                $orderGoodssNum += $orderGoods["number"];
                            }

                            if (
                                $sku["purchase_limit"] > 0 &&
                                $orderGoodssNum >= $sku["purchase_limit"]
                            ) {
                                throw new Exception(
                                    "{$sku["difference"]} 限制 {$sku["purchase_limit"]} 单"
                                );
                            }

                            // 1.1.2升级
                            $sku_key =
                                "goods_" . $sku["goods_id"] . "_" . $sku["id"];
                            // 1.1.0升级
                            if (!$goods) {
                                throw new Exception(
                                    "对不起当前商品不存在或已下架"
                                );
                            }
                            // 效验shop_id是否正确 1.1.2升级
                            if ($goods["shop_id"] != $shop_id) {
                                throw new Exception("网络异常SHOPID错误！");
                            }
                            if ($goods["is_free_goods"] == 1) {
                                $order->order_type = 2;
                                $order->save();
                            }
                            // 1.1.2升级 提交订单判断库存
                            if ($sku["stock"] <= 0) {
                                throw new Exception("商品被抢光了");
                            } elseif ($sku["stock"] < $data["number"]) {
                                throw new Exception("库存不足");
                            }
                            // 库存计算方式:porder=下单减库存,payment=付款减库存
                            if ($goods["stock"] == "porder") {
                                // 1.1.2升级
                                if (
                                    $data["number"] > $redis->llen("{$sku_key}")
                                ) {
                                    throw new Exception(
                                        "系统繁忙，请稍后抢购！"
                                    );
                                } else {
                                    for ($i = 0; $i < $data["number"]; $i++) {
                                        $redis->rpop("{$sku_key}");
                                    }
                                    $sku->setDec("stock", $data["number"]); // 1.0.3升级
                                }
                            }
                            // 生成运费
                            $freight = $this->freight(
                                $goods["freight_id"],
                                $sku["weigh"],
                                $data["number"],
                                $address["city"],
                                true
                            );
                            // 商品列表 actual_payment
                            $shopGoodsAll[] = [
                                "order_id" => $order->id, // 获取自增ID
                                "brand_name" => $goods["brand"]["name"],
                                "goods_id" => $goods["id"],
                                "shop_id" => $shop_id,
                                "title" => $goods["title"],
                                "image" => $goods["image"],
                                "goods_sku_sn" => $sku["sn"],
                                "goods_sku_id" => $sku["id"],
                                "difference" => join(",", $sku["difference"]),
                                "market_price" => $sku["market_price"], // 市场价
                                "price" => $sku["price"], // 原价
                                "freight_price" => $freight["price"], //快递价格
                                "discount_price" => 0, // 优惠金额
                                "actual_payment" => bcmul(
                                    $sku["price"],
                                    $data["number"],
                                    2
                                ), // 1.0.6修复 实际支付，因为要和总价进行计算
                                "number" => $data["number"],
                            ];
                            $freightALL[] = $freight;
                            $priceAll = bcadd(
                                $priceAll,
                                bcmul($sku["price"], $data["number"], 2),
                                2
                            ); // 计算价格
                            $numberAll += $data["number"]; // 计算数量
                        }
                        // 计算运费叠加方案
                        if ($config["freight"] == 0) {
                            // 运费叠加
                            $freight = [
                                "id" => $freightALL[0]["id"],
                                "name" => "合并运费",
                                "price" => array_sum(
                                    array_column($freightALL, "price")
                                ),
                            ];
                        } elseif ($config["freight"] == 1) {
                            // 以最低结算
                            array_multisort(
                                array_column($freightALL, "price"),
                                SORT_ASC,
                                $freightALL
                            );
                            $freight = [
                                "id" => $freightALL[0]["id"],
                                "name" => $freightALL[0]["name"],
                                "price" => $freightALL[0]["price"],
                            ];
                        } elseif ($config["freight"] == 2) {
                            // 以最高结算
                            array_multisort(
                                array_column($freightALL, "price"),
                                SORT_DESC,
                                $freightALL
                            );
                            $freight = [
                                "id" => $freightALL[0]["id"],
                                "name" => $freightALL[0]["name"],
                                "price" => $freightALL[0]["price"],
                            ];
                        }
                        $freight_price = $freight["price"]; //快递金额
                        $price = bcadd($priceAll, $freight_price, 2); // 总价格
                        $coupon_price = 0; //优惠券金额
                        $discount_price = 0; // 优惠金额，因为后续版本涉及到活动减免，所以优惠金额要单独拎出来

                        // 如果优惠券存在
                        if ($coupon) {
                            // 判断是否可用
                            if ($priceAll >= $coupon["limit"]) {
                                // 优惠金额
                                if (
                                    $coupon["type"] == "reduction" ||
                                    ($coupon["type"] == "vip" &&
                                        $coupon["usertype"] == "reduction")
                                ) {
                                    $coupon_price = $coupon["price"];
                                    //总金额 =（订单金额 - 优惠券金额）+ 运费
                                    $price = bcadd(
                                        bcsub($priceAll, $coupon_price, 2),
                                        $freight["price"],
                                        2
                                    );
                                }
                                // 折扣金额
                                if (
                                    $coupon["type"] == "discount" ||
                                    ($coupon["type"] == "vip" &&
                                        $coupon["usertype"] == "discount")
                                ) {
                                    // 排除异常折扣，还原百分之
                                    $discount =
                                        $coupon["discount"] > 10
                                            ? bcdiv($coupon["discount"], 100, 2)
                                            : bcdiv($coupon["discount"], 10, 2);
                                    // 优惠金额 = 订单金额 - 订单金额 * 折扣
                                    $coupon_price = bcsub(
                                        $priceAll,
                                        bcmul($priceAll, $discount, 2),
                                        2
                                    );
                                    $price = bcadd(
                                        bcsub($priceAll, $coupon_price, 2),
                                        $freight["price"],
                                        2
                                    );
                                }
                                // 免邮金额
                                if ($coupon["type"] == "shipping") {
                                    $coupon_price = $freight_price;
                                    $price = $priceAll;
                                    $freight_price = 0;
                                }
                                $discount_price = $coupon_price;

                                // 总优惠金额 1.1.3弃用
                                // $paycoupon = 0;
                                // 总实际支付金额 1.1.3弃用
                                // $payment = 0;
                                foreach ($shopGoodsAll as $row) {
                                    $goods_price = bcmul(
                                        $row["price"],
                                        $row["number"],
                                        2
                                    );
                                    $goods_discount_price = round(
                                        $coupon_price *
                                            ($goods_price / $priceAll),
                                        2
                                    ); // 优惠金额
                                    // 1.0.8升级,修复包邮
                                    $actual_payment =
                                        $coupon["type"] === "shipping"
                                            ? $goods_price
                                            : bcsub(
                                                $goods_price,
                                                $goods_discount_price,
                                                2
                                            );
                                    //优惠价格
                                    $row[
                                        "discount_price"
                                    ] = $goods_discount_price;
                                    // 实际支付 1.0.9升级
                                    $row["actual_payment"] =
                                        $actual_payment <= 0
                                            ? "0.01"
                                            : $actual_payment;
                                    $row["freight_price"] = $freight_price;
                                    // 1.0.8升级 1.1.3弃用
                                    // $paycoupon = bcadd($paycoupon, $goods_discount_price, 2);
                                    // $payment = bcadd($payment, $actual_payment, 2);
                                    $goodsList[] = $row;
                                }

                                // 更新已使用数量
                                model("app\api\model\wanlshop\Coupon")
                                    ->where(["id" => $coupon["coupon_id"]])
                                    ->setInc("usenum");
                                // 修改该优惠券状态 已用
                                $coupon->allowField(true)->save(["state" => 2]);
                            } else {
                                model("app\api\model\wanlshop\Order")->destroy(
                                    $order->id
                                );
                                throw new Exception(
                                    "订单金额" .
                                        $priceAll .
                                        "元，不满" .
                                        $coupon["limit"] .
                                        "元"
                                );
                            }
                        } else {
                            foreach ($shopGoodsAll as $row) {
                                $goodsList[] = $row;
                            }
                        }
                        // 生成支付
                        $payList[] = [
                            "user_id" => $user_id,
                            "shop_id" => $shop_id,
                            "order_id" => $order->id,
                            "order_no" => $order->order_no,
                            "pay_no" => $order->order_no,
                            "type" => "goods", // 1.0.8升级
                            "price" => $price <= 0 ? 0.01 : $price, // 支付价格，系统要求至少支付一分钱
                            "order_price" => $priceAll, // 订单总金额
                            "coupon_price" => $coupon_price, // 优惠券金额
                            "freight_price" => $freight_price, // 快递费
                            "discount_price" => $discount_price, // 优惠金额
                            "number" => $numberAll,
                        ];
                        // 生成地址
                        $addressList[] = [
                            "user_id" => $user_id,
                            "shop_id" => $shop_id,
                            "order_id" => $order->id,
                            "name" => $address["name"],
                            "mobile" => $address["mobile"],
                            "address" =>
                                $address["province"] .
                                "/" .
                                $address["city"] .
                                "/" .
                                $address["district"] .
                                "/" .
                                $address["address"],
                            "address_name" => $address["address_name"],
                            "location" => $address["location"],
                        ];

                        // 注释：取货码生成已移至支付成功后
                        // 如果是自提方式，在支付成功后才生成核销码
                        // 取货码将在支付回调中生成，确保只有已支付订单才有取货码
                    } else {
                        throw new Exception("网络繁忙，创建订单失败！");
                    }
                }
                model("app\api\model\wanlshop\OrderAddress")->saveAll(
                    $addressList
                );
                model("app\api\model\wanlshop\OrderGoods")->saveAll($goodsList);
                $result = model("app\api\model\wanlshop\Pay")->saveAll(
                    $payList
                );
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success("返回成功", [
                    "list" => $result,
                    "token" => Common::creatToken(
                        "orderToken_" . $this->auth->id
                    ),
                ]);
            } else {
                $this->error(__("订单异常，请返回重新下单"));
            }
        } else {
            $this->error(__("非法请求"));
        }
    }

    /**
     * 核销自提订单（重定向到新的核销接口）
     *
     * @ApiSummary  (WanlShop 订单接口核销自提订单)
     * @ApiMethod   (POST)
     *
     * @param string $pickup_code 核销码
     */
    public function verifyPickupCode()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $pickup_code = $this->request->post("pickup_code");
            if (!$pickup_code) {
                $this->error(__("请输入核销码"));
            }

            // 使用新的核销码系统
            $result = \app\api\model\wanlshop\PickupCode::verifyCode(
                $pickup_code,
                $this->auth->id
            );

            if ($result["success"]) {
                $pickupCodeData = $result["data"];
                $this->success($result["message"], [
                    "order_id" => $pickupCodeData->order_id,
                    "order_type" => $pickupCodeData->order_type,
                    "pickup_code" => $pickup_code,
                    "verify_time" => date(
                        "Y-m-d H:i:s",
                        $pickupCodeData->verify_time
                    ),
                ]);
            } else {
                $this->error($result["message"]);
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 订单状态码（方法内使用）
     *
     * @ApiSummary  (WanlShop 返回订单状态码)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    private function getOrderState($id = 0)
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        $order = model("app\api\model\wanlshop\Order")
            ->where(["id" => $id, "user_id" => $this->auth->id])
            ->find();
        return $order["state"];
    }

    /**
     * 获取运费模板和子类
     * -- 内部方法
     * 1.1.6升级
     * @param string $id 运费ID
     * @param string $weigh 商品重量
     * @param string $number 商品数量
     * @param string $city 邮递城市
     */
    public function freight(
        $id = null,
        $weigh = null,
        $number = 0,
        $city = "南山区",
        $is_verify_delivery = false
    ) {
        // 运费模板
        $data = model("app\api\model\wanlshop\ShopFreight")
            ->where("id", $id)
            ->field("id,delivery,isdelivery,name,valuation")
            ->find();

        // 如果运费模板不存在，自动创建免费模板
        if (!$data && $id > 0) {
            // 通过运费模板ID无法找到，可能是数据不一致
            // 这种情况下创建一个通用的免费模板
            $this->error(__("此商品运费模板不存在，暂不支持下单"));
        } elseif (!$data && $id == 0) {
            // freight_id为0的情况，需要从调用上下文获取shop_id
            // 这里我们返回一个默认的免费运费结构
            $data = [
                'id' => 0,
                'delivery' => '5',
                'isdelivery' => '1', // 包邮
                'name' => '系统默认免费模板',
                'valuation' => '0'
            ];
        }
        $data["price"] = 0;
        // 是否包邮:0=自定义运费,1=卖家包邮
        if ($data["isdelivery"] == 0) {
            // 获取地址编码 1.1.0升级
            $area = model("app\common\model\Area")->get(["name" => $city]);
            if (!$area) {
                return $data;
            }

            $list = model("app\api\model\wanlshop\ShopFreightData")
                ->where(
                    "FIND_IN_SET(:area_id, citys) AND freight_id = :freight_id",
                    ["area_id" => $area->id, "freight_id" => $id]
                )
                ->find();

            if (!$list) {
                return $data;
            }

            //          检查是否可配送
            if ($list["is_delivery"] == 0 && $is_verify_delivery == true) {
                $this->error(__("该地区暂不配送"));
            }

            // 计价方式:0=按件数,1=按重量,2=按体积  1.0.2升级
            if ($data["valuation"] == 0) {
                if ($number <= $list["first"]) {
                    $price = $list["first_fee"];
                } else {
                    $additional =
                        $list["additional"] > 0 ? $list["additional"] : 1; //因为要更换vue后台，临时方案，为防止客户填写0
                    $price = bcadd(
                        bcmul(
                            ceil(($number - $list["first"]) / $additional),
                            $list["additional_fee"],
                            2
                        ),
                        $list["first_fee"],
                        2
                    );
                }
            } else {
                $weigh = $weigh * $number; // 订单总重量
                if ($weigh <= $list["first"]) {
                    // 如果重量小于等首重，则首重价格
                    $price = $list["first_fee"];
                } else {
                    $additional =
                        $list["additional"] > 0 ? $list["additional"] : 1;
                    $price = bcadd(
                        bcmul(
                            ceil(($weigh - $list["first"]) / $additional),
                            $list["additional_fee"],
                            2
                        ),
                        $list["first_fee"],
                        2
                    );
                }
            }
            $data["price"] = $price;
        }
        return $data;
    }

    /**
     * 为店铺创建默认免费运费模板
     * @param int $shopId 店铺ID
     * @return int|false 返回创建的运费模板ID，失败返回false
     */
    protected function createDefaultFreeFreightTemplate($shopId)
    {
        try {
            // 检查该店铺是否已有默认免费模板
            $existingTemplate = model("app\api\model\wanlshop\ShopFreight")
                ->where("shop_id", $shopId)
                ->where("name", "系统默认免费模板")
                ->where("isdelivery", "1")
                ->field("id")
                ->find();

            if ($existingTemplate) {
                return $existingTemplate['id'];
            }

            // 创建新的免费运费模板
            $freightData = [
                'shop_id' => $shopId,
                'name' => '系统默认免费模板',
                'delivery' => '5', // 1天内发货
                'isdelivery' => '1', // 卖家包邮
                'valuation' => '0', // 按件数计价
                'status' => 'normal',
                'createtime' => time(),
                'updatetime' => time()
            ];

            $freightModel = model("app\api\model\wanlshop\ShopFreight");
            if ($freightModel->save($freightData)) {
                return $freightModel->id;
            }

            return false;

        } catch (\Exception $e) {
            // 记录错误日志但不中断流程
            \think\Log::error("创建默认运费模板失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 为店铺创建默认配置
     * @param int $shopId 店铺ID
     * @return object|false 返回创建的配置对象，失败返回false
     */
    protected function createDefaultShopConfig($shopId)
    {
        try {
            // 检查该店铺是否已有配置（避免重复创建）
            $existingConfig = model("app\api\model\wanlshop\ShopConfig")
                ->where("shop_id", $shopId)
                ->find();

            if ($existingConfig) {
                return $existingConfig;
            }

            // 创建新的店铺配置
            $configData = [
                'shop_id' => $shopId,
                'freight' => '1', // 以最低结算（最常用的策略）
                'category_style' => '1', // 一级类目大图
                'iscloud' => '0', // 关闭云打印
                'isauto' => '0', // 关闭自动发货
                'createtime' => time(),
                'updatetime' => time()
            ];

            $configModel = model("app\api\model\wanlshop\ShopConfig");
            if ($configModel->save($configData)) {
                return $configModel;
            }

            return false;

        } catch (\Exception $e) {
            // 记录错误日志但不中断流程
            \think\Log::error("创建默认店铺配置失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 退还订单的抵扣余额
     * @param int $order_id 订单ID
     * @param int $user_id 用户ID
     * @param string $reason 退还原因
     * @return bool
     */
    private function refundDeductionBalanceForOrder($order_id, $user_id, $reason = '订单取消退还抵扣余额')
    {
        try {
            // 查询该订单的支付记录
            $payRecords = \think\Db::name('wanlshop_pay')
                ->where([
                    'order_id' => $order_id,
                    'user_id' => $user_id,
                    'type' => 'goods'
                ])
                ->select();

            if (empty($payRecords)) {
                return true; // 没有支付记录，直接返回成功
            }

            $totalRefundAmount = 0;
            foreach ($payRecords as $pay) {
                if ($pay['deduction_amount'] > 0) {
                    // 退还抵扣余额
                    $result = \app\common\model\User::deductionBalance(
                        $pay['deduction_amount'],
                        $user_id,
                        $reason . ':订单' . $pay['order_no'],
                        'refund',
                        $order_id,
                        $pay['id']
                    );

                    if (!$result) {
                        throw new \Exception("退还抵扣余额失败，支付记录ID：{$pay['id']}");
                    }

                    $totalRefundAmount += $pay['deduction_amount'];
                }
            }

            if ($totalRefundAmount > 0) {
                \think\Log::info("订单取消退还抵扣余额成功: " . json_encode([
                    'order_id' => $order_id,
                    'user_id' => $user_id,
                    'refund_amount' => $totalRefundAmount,
                    'reason' => $reason
                ], JSON_UNESCAPED_UNICODE));
            }

            return true;
        } catch (\Exception $e) {
            \think\Log::error("订单取消退还抵扣余额失败: " . json_encode([
                'order_id' => $order_id,
                'user_id' => $user_id,
                'error' => $e->getMessage(),
                'reason' => $reason
            ], JSON_UNESCAPED_UNICODE));
            throw $e;
        }
    }

    /**
     * 扣除订单赠送的抵扣券
     * @param int $order_id 订单ID
     * @param int $user_id 用户ID
     * @return array 处理结果
     */
    private function deductRewardedBalanceForOrder($order_id, $user_id)
    {
        try {
            // 查找该订单的赠送抵扣余额记录
            $rewardLogs = \think\Db::name('deduction_balance_log')
                ->where([
                    'user_id' => $user_id,
                    'type' => 'reward',
                    'order_id' => $order_id
                ])
                ->where('amount', '>', 0)
                ->select();

            if (empty($rewardLogs)) {
                // 没有赠送记录，直接返回成功
                return [
                    'success' => true,
                    'deducted' => false,
                    'message' => '订单取消成功！'
                ];
            }

            // 计算总的赠送金额
            $totalRewardAmount = 0;
            foreach ($rewardLogs as $log) {
                $totalRewardAmount += $log['amount'];
            }

            if ($totalRewardAmount <= 0) {
                return [
                    'success' => true,
                    'deducted' => false,
                    'message' => '订单取消成功！'
                ];
            }

            // 检查用户当前抵扣余额是否充足
            $user = \app\common\model\User::find($user_id);
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            if ($user->deduction_balance < $totalRewardAmount) {
                // 余额不足，不允许取消订单
                return [
                    'success' => false,
                    'deducted' => false,
                    'message' => "由于本次订单曾赠送您{$totalRewardAmount}元抵扣券，当前账户券余额不足抵扣，订单无法取消，如有疑问请联系客服！"
                ];
            }

            // 执行扣除操作
            $result = \app\common\model\User::deductionBalance(
                -$totalRewardAmount, // 负数表示扣除
                $user_id,
                "取消订单扣除赠送抵扣券:订单{$order_id}",
                'refund',
                $order_id,
                null
            );

            if (!$result) {
                throw new \Exception('扣除赠送抵扣券失败');
            }

            // 记录日志
            \think\Log::info("订单取消扣除赠送抵扣券成功: " . json_encode([
                'order_id' => $order_id,
                'user_id' => $user_id,
                'deduct_amount' => $totalRewardAmount
            ], JSON_UNESCAPED_UNICODE));

            return [
                'success' => true,
                'deducted' => true,
                'message' => "订单取消成功！您已退还商品金额，同时系统已自动收回赠送的{$totalRewardAmount}元抵扣券。剩余券余额可在【我的抵扣券】中查看"
            ];

        } catch (\Exception $e) {
            \think\Log::error("订单取消扣除赠送抵扣券失败: " . json_encode([
                'order_id' => $order_id,
                'user_id' => $user_id,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE));

            return [
                'success' => false,
                'deducted' => false,
                'message' => '扣除赠送抵扣券失败: ' . $e->getMessage()
            ];
        }
    }
}
