# 取消订单抵扣券扣除功能实现说明

## 功能概述

在小程序订单取消时，增加对系统赠送抵扣券的扣除逻辑。当用户取消订单时，系统会自动扣除该订单曾经赠送的抵扣券，如果用户抵扣券余额不足，则不允许取消订单。

## 需求场景

### 场景描述
- 用户购买100元产品，支付成功后系统赠送8元抵扣券
- 用户后续想要取消订单时，需要扣除之前赠送的8元抵扣券

### 处理逻辑

#### 场景1：抵扣券余额充足时
- **操作**：直接扣除成功
- **提示消息**：`订单取消成功！您已退还商品金额，同时系统已自动收回赠送的8元抵扣券。剩余券余额可在【我的抵扣券】中查看`

#### 场景2：抵扣券余额不足时
- **操作**：阻止取消订单
- **提示消息**：`由于本次订单曾赠送您8元抵扣券，当前账户券余额不足抵扣，订单无法取消，如有疑问请联系客服！`

## 技术实现

### 修改的文件
- **文件路径**：`application/api/controller/wanlshop/Order.php`
- **修改方法**：`cancelOrder()`
- **新增方法**：`deductRewardedBalanceForOrder()`

### 核心实现逻辑

#### 1. 修改取消订单主流程
```php
// 在 cancelOrder() 方法中增加以下逻辑：

// 初始化扣除结果
$deductResult = ['deducted' => false, 'message' => '订单取消成功！'];

\think\Db::startTrans();
try {
    // ... 原有逻辑 ...
    
    // 退还抵扣余额（原有逻辑）
    $this->refundDeductionBalanceForOrder($id, $this->auth->id, '取消订单退还抵扣余额');

    // 扣除赠送的抵扣券（新增逻辑）
    $deductResult = $this->deductRewardedBalanceForOrder($id, $this->auth->id);
    if (!$deductResult['success']) {
        throw new \Exception($deductResult['message']);
    }

    \think\Db::commit();
} catch (\Exception $e) {
    \think\Db::rollback();
    $this->error("取消订单失败：" . $e->getMessage());
}

// 根据是否扣除了抵扣券返回不同的成功消息
$successMessage = $deductResult['deducted'] ? $deductResult['message'] : "订单取消成功！";
$this->success($successMessage, $result ? true : false);
```

#### 2. 新增抵扣券扣除方法
```php
/**
 * 扣除订单赠送的抵扣券
 * @param int $order_id 订单ID
 * @param int $user_id 用户ID
 * @return array 处理结果
 */
private function deductRewardedBalanceForOrder($order_id, $user_id)
{
    try {
        // 1. 查找该订单的赠送抵扣余额记录
        $rewardLogs = \think\Db::name('deduction_balance_log')
            ->where([
                'user_id' => $user_id,
                'type' => 'reward',
                'order_id' => $order_id
            ])
            ->where('amount', '>', 0)
            ->select();

        if (empty($rewardLogs)) {
            // 没有赠送记录，直接返回成功
            return [
                'success' => true,
                'deducted' => false,
                'message' => '订单取消成功！'
            ];
        }

        // 2. 计算总的赠送金额
        $totalRewardAmount = 0;
        foreach ($rewardLogs as $log) {
            $totalRewardAmount += $log['amount'];
        }

        // 3. 检查用户当前抵扣余额是否充足
        $user = \app\common\model\User::find($user_id);
        if ($user->deduction_balance < $totalRewardAmount) {
            // 余额不足，不允许取消订单
            return [
                'success' => false,
                'deducted' => false,
                'message' => "由于本次订单曾赠送您{$totalRewardAmount}元抵扣券，当前账户券余额不足抵扣，订单无法取消，如有疑问请联系客服！"
            ];
        }

        // 4. 执行扣除操作
        $result = \app\common\model\User::deductionBalance(
            -$totalRewardAmount, // 负数表示扣除
            $user_id,
            "取消订单扣除赠送抵扣券:订单{$order_id}",
            'refund',
            $order_id,
            null
        );

        if (!$result) {
            throw new \Exception('扣除赠送抵扣券失败');
        }

        return [
            'success' => true,
            'deducted' => true,
            'message' => "订单取消成功！您已退还商品金额，同时系统已自动收回赠送的{$totalRewardAmount}元抵扣券。剩余券余额可在【我的抵扣券】中查看"
        ];

    } catch (\Exception $e) {
        return [
            'success' => false,
            'deducted' => false,
            'message' => '扣除赠送抵扣券失败: ' . $e->getMessage()
        ];
    }
}
```

### 数据库依赖

#### 相关表结构
1. **zy_deduction_balance_log** - 抵扣余额日志表
   - 用于查找订单的赠送记录
   - 关键字段：`user_id`, `order_id`, `type`, `amount`

2. **zy_user** - 用户表
   - 用于检查用户当前抵扣余额
   - 关键字段：`deduction_balance`

3. **zy_wanlshop_order** - 订单表
   - 用于验证订单状态和权限
   - 关键字段：`id`, `user_id`, `state`

### 业务流程图

```
开始取消订单
    ↓
检查订单状态和权限
    ↓
开启数据库事务
    ↓
执行原有取消逻辑
    ↓
退还用户使用的抵扣余额
    ↓
查找订单赠送的抵扣券记录
    ↓
是否有赠送记录？
    ├─ 否 → 直接提交事务 → 返回成功
    └─ 是 ↓
检查用户抵扣余额是否充足？
    ├─ 否 → 回滚事务 → 返回余额不足错误
    └─ 是 ↓
扣除赠送的抵扣券
    ↓
提交事务
    ↓
返回成功（包含扣除信息）
```

## 测试验证

### 测试环境准备
1. 确保数据库表结构完整
2. 准备测试用户和订单数据
3. 确保用户有足够的抵扣余额进行测试

### 测试用例

#### 用例1：余额充足的取消订单
1. **前置条件**：用户有订单且抵扣余额充足
2. **操作步骤**：调用取消订单API
3. **预期结果**：
   - 订单状态变为已取消
   - 用户抵扣余额减少对应金额
   - 返回包含扣除信息的成功消息

#### 用例2：余额不足的取消订单
1. **前置条件**：用户有订单但抵扣余额不足
2. **操作步骤**：调用取消订单API
3. **预期结果**：
   - 订单状态不变
   - 用户抵扣余额不变
   - 返回余额不足的错误消息

#### 用例3：无赠送记录的取消订单
1. **前置条件**：用户有订单但该订单无赠送记录
2. **操作步骤**：调用取消订单API
3. **预期结果**：
   - 订单正常取消
   - 返回普通的成功消息

### API接口信息
- **接口地址**：`POST /wanlshop/order/cancelOrder`
- **请求参数**：`{"id": "订单ID"}`
- **认证要求**：需要用户登录
- **返回格式**：JSON

## 注意事项

1. **事务完整性**：所有操作都在数据库事务中执行，确保数据一致性
2. **错误处理**：完善的异常捕获和错误提示
3. **日志记录**：重要操作都有详细的日志记录
4. **向后兼容**：不影响现有的取消订单逻辑
5. **用户体验**：提供清晰友好的提示信息

## 部署建议

1. **测试环境验证**：在测试环境充分验证功能
2. **数据备份**：部署前备份相关数据表
3. **灰度发布**：建议先对部分用户开放测试
4. **监控告警**：关注相关错误日志和用户反馈

---

**实现时间**：2025-01-31  
**实现人员**：AI Assistant  
**状态**：✅ 已完成
