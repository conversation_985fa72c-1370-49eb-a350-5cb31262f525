<?php
/**
 * 简化的取消订单功能测试
 */

// 直接连接数据库测试
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'dbname' => 'elecloud',
    'username' => 'root',
    'password' => 'sl331639',
    'charset' => 'utf8mb4'
];

try {
    echo "=== 取消订单抵扣券扣除功能测试 ===\n\n";

    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
    ]);

    echo "✅ 数据库连接成功\n\n";

    echo "1. 检查相关表结构...\n";
    
    // 检查订单表
    $stmt = $pdo->query("SHOW TABLES LIKE 'zy_wanlshop_order'");
    $orderTableExists = $stmt->fetch();
    echo $orderTableExists ? "✅ 订单表存在\n" : "❌ 订单表不存在\n";
    
    // 检查抵扣余额日志表
    $stmt = $pdo->query("SHOW TABLES LIKE 'zy_deduction_balance_log'");
    $logTableExists = $stmt->fetch();
    echo $logTableExists ? "✅ 抵扣余额日志表存在\n" : "❌ 抵扣余额日志表不存在\n";
    
    // 检查用户表
    $stmt = $pdo->query("SHOW TABLES LIKE 'zy_user'");
    $userTableExists = $stmt->fetch();
    echo $userTableExists ? "✅ 用户表存在\n" : "❌ 用户表不存在\n";
    
    echo "\n2. 查找测试数据...\n";
    
    if ($userTableExists) {
        // 查找有抵扣余额的用户
        $stmt = $pdo->query("SELECT id, mobile, deduction_balance FROM zy_user WHERE deduction_balance > 0 LIMIT 3");
        $users = $stmt->fetchAll();
        
        if (empty($users)) {
            echo "❌ 没有找到有抵扣余额的用户\n";
        } else {
            echo "✅ 找到有抵扣余额的用户:\n";
            foreach ($users as $user) {
                echo "   - 用户ID: {$user['id']}, 手机号: {$user['mobile']}, 抵扣余额: {$user['deduction_balance']} 元\n";
            }
        }
    }
    
    echo "\n";
    
    if ($logTableExists) {
        // 查找赠送记录
        $stmt = $pdo->query("SELECT user_id, order_id, amount, memo, createtime FROM zy_deduction_balance_log WHERE type = 'reward' AND amount > 0 ORDER BY createtime DESC LIMIT 5");
        $rewardLogs = $stmt->fetchAll();
        
        if (empty($rewardLogs)) {
            echo "❌ 没有找到抵扣余额赠送记录\n";
        } else {
            echo "✅ 找到抵扣余额赠送记录:\n";
            foreach ($rewardLogs as $log) {
                $time = date('Y-m-d H:i:s', $log['createtime']);
                echo "   - 用户ID: {$log['user_id']}, 订单ID: {$log['order_id']}, 金额: +{$log['amount']} 元, 时间: {$time}\n";
            }
        }
    }

    echo "\n3. 功能实现说明...\n";
    echo "📋 修改的文件: application/api/controller/wanlshop/Order.php\n";
    echo "🔧 修改的方法: cancelOrder()\n";
    echo "➕ 新增的方法: deductRewardedBalanceForOrder()\n";
    echo "\n";
    
    echo "🎯 实现的功能:\n";
    echo "   1. 取消订单时查找该订单的赠送抵扣券记录\n";
    echo "   2. 检查用户当前抵扣余额是否充足\n";
    echo "   3. 余额充足时：扣除赠送的抵扣券并返回成功消息\n";
    echo "   4. 余额不足时：阻止取消并返回友好提示\n";
    echo "\n";
    
    echo "💬 用户提示消息:\n";
    echo "   场景1（余额充足）: 订单取消成功！您已退还商品金额，同时系统已自动收回赠送的X元抵扣券。剩余券余额可在【我的抵扣券】中查看\n";
    echo "   场景2（余额不足）: 由于本次订单曾赠送您X元抵扣券，当前账户券余额不足抵扣，订单无法取消，如有疑问请联系客服！\n";
    echo "\n";
    
    echo "🧪 测试方法:\n";
    echo "   1. 在小程序中下单并支付（获得赠送抵扣券）\n";
    echo "   2. 尝试取消订单\n";
    echo "   3. 观察返回的消息是否符合预期\n";
    echo "   4. 检查用户抵扣余额变化\n";
    echo "\n";
    
    echo "📡 API接口信息:\n";
    echo "   接口地址: POST /wanlshop/order/cancelOrder\n";
    echo "   请求参数: {\"id\": \"订单ID\"}\n";
    echo "   认证要求: 需要用户登录\n";
    echo "   返回格式: JSON\n";

    echo "\n✅ 功能已实现完成！\n";
    echo "💡 建议：在生产环境部署前，请先在测试环境充分验证功能\n";

} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}
