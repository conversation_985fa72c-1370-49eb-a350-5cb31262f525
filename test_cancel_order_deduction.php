<?php
/**
 * 测试取消订单抵扣券扣除功能
 */

// 设置基本路径
define('APP_PATH', __DIR__ . '/application/');
define('ROOT_PATH', __DIR__ . '/');

// 引入ThinkPHP
require __DIR__ . '/thinkphp/start.php';

use think\Db;
use app\common\model\User;

try {
    echo "=== 取消订单抵扣券扣除功能测试 ===\n\n";

    // 测试用户ID（请根据实际情况修改）
    $testUserId = 1;
    
    echo "1. 检查测试用户信息...\n";
    $user = User::find($testUserId);
    if (!$user) {
        echo "❌ 测试用户不存在，请修改 \$testUserId 变量\n";
        exit;
    }
    
    echo "✅ 测试用户: ID={$user->id}, 手机号={$user->mobile}\n";
    echo "   当前抵扣余额: {$user->deduction_balance} 元\n\n";

    echo "2. 查找用户的订单记录...\n";
    $orders = Db::name('wanlshop_order')
        ->where('user_id', $testUserId)
        ->where('state', 1) // 待支付状态
        ->limit(3)
        ->select();
    
    if (empty($orders)) {
        echo "❌ 没有找到待支付的订单\n";
        echo "   提示：请先创建一个待支付的订单进行测试\n\n";
    } else {
        echo "✅ 找到 " . count($orders) . " 个待支付订单:\n";
        foreach ($orders as $order) {
            echo "   - 订单ID: {$order['id']}, 订单号: {$order['order_no']}, 金额: {$order['price']} 元\n";
        }
        echo "\n";
    }

    echo "3. 查找用户的抵扣余额赠送记录...\n";
    $rewardLogs = Db::name('deduction_balance_log')
        ->where('user_id', $testUserId)
        ->where('type', 'reward')
        ->where('amount', '>', 0)
        ->order('createtime DESC')
        ->limit(5)
        ->select();
    
    if (empty($rewardLogs)) {
        echo "❌ 没有找到抵扣余额赠送记录\n";
        echo "   提示：请先完成一次支付以获得赠送的抵扣余额\n\n";
    } else {
        echo "✅ 找到 " . count($rewardLogs) . " 条赠送记录:\n";
        foreach ($rewardLogs as $log) {
            $orderInfo = $log['order_id'] ? "订单ID: {$log['order_id']}" : "无关联订单";
            echo "   - 金额: +{$log['amount']} 元, {$orderInfo}, 时间: " . date('Y-m-d H:i:s', $log['createtime']) . "\n";
        }
        echo "\n";
    }

    echo "4. 模拟测试取消订单的抵扣券扣除逻辑...\n";
    
    if (!empty($orders) && !empty($rewardLogs)) {
        $testOrderId = $orders[0]['id'];
        
        // 查找该订单的赠送记录
        $orderRewardLogs = Db::name('deduction_balance_log')
            ->where([
                'user_id' => $testUserId,
                'type' => 'reward',
                'order_id' => $testOrderId
            ])
            ->where('amount', '>', 0)
            ->select();
        
        if (empty($orderRewardLogs)) {
            echo "   ⚠️  订单ID {$testOrderId} 没有对应的赠送记录\n";
            echo "   提示：这是正常的，因为只有支付成功的订单才会有赠送记录\n\n";
        } else {
            $totalRewardAmount = 0;
            foreach ($orderRewardLogs as $log) {
                $totalRewardAmount += $log['amount'];
            }
            
            echo "   📊 订单ID {$testOrderId} 的赠送记录:\n";
            echo "   - 总赠送金额: {$totalRewardAmount} 元\n";
            echo "   - 用户当前余额: {$user->deduction_balance} 元\n";
            
            if ($user->deduction_balance >= $totalRewardAmount) {
                echo "   ✅ 余额充足，可以取消订单\n";
                echo "   💡 预期消息: 订单取消成功！您已退还商品金额，同时系统已自动收回赠送的{$totalRewardAmount}元抵扣券。剩余券余额可在【我的抵扣券】中查看\n";
            } else {
                echo "   ❌ 余额不足，无法取消订单\n";
                echo "   💡 预期消息: 由于本次订单曾赠送您{$totalRewardAmount}元抵扣券，当前账户券余额不足抵扣，订单无法取消，如有疑问请联系客服！\n";
            }
        }
    }

    echo "\n5. 功能实现要点检查...\n";
    
    // 检查相关表是否存在
    $tables = [
        'wanlshop_order' => '订单表',
        'deduction_balance_log' => '抵扣余额日志表',
        'user' => '用户表'
    ];
    
    foreach ($tables as $table => $desc) {
        $exists = Db::query("SHOW TABLES LIKE 'zy_{$table}'");
        if ($exists) {
            echo "   ✅ {$desc} (zy_{$table}) 存在\n";
        } else {
            echo "   ❌ {$desc} (zy_{$table}) 不存在\n";
        }
    }

    echo "\n6. API接口测试说明...\n";
    echo "   📡 接口地址: POST /wanlshop/order/cancelOrder\n";
    echo "   📝 请求参数: {\"id\": \"订单ID\"}\n";
    echo "   🔐 需要用户登录认证\n";
    echo "   \n";
    echo "   测试步骤:\n";
    echo "   1. 用户下单并支付（获得赠送抵扣券）\n";
    echo "   2. 调用取消订单接口\n";
    echo "   3. 检查返回消息是否符合预期\n";
    echo "   4. 验证用户抵扣余额是否正确扣除\n";

    echo "\n✅ 测试完成！\n";
    echo "💡 提示：实际测试请使用小程序或API工具调用取消订单接口\n";

} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
